<template>
  <form>
    <div
      class="tw:grid tw:grid-cols-1 tw:tl:grid-cols-2 tw:mt-2 tw:gap-x-[4rem] tw:gap-y-[3rem] tw:tl:gap-y-[2.5rem] tw:text-[#333333]"
    >
      <div>
        <div class="tw:mb-1 tw:text-xs-design">事業者区分</div>
        <BaseSingleSelectInput
          v-model="searchFormProvideData.form.value.type"
          :options="ENTERPRISE_TYPE_OPTIONS"
          :error="!!searchFormProvideData.errors.value.type"
          :error-message="searchFormProvideData.errors.value.type"
          :use-input="false"
          emit-value
          map-options
          @clear="searchFormProvideData.form.value.type = ''"
          :is-clearable="!!searchFormProvideData.form.value.type"
          input-class="tw:text-m-design"
          class="tw:text-m-design"
        />
      </div>
      <div class="tw:pt-3 tw:tl:pt-0">
        <div class="tw:mb-1 tw:text-xs-design">事業者名</div>
        <BaseInput
          v-model="searchFormProvideData.form.value.name"
          outlined
          input-class="tw:text-[#333333] tw:text-m-design"
          maxlength="50"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.name"
          :error-message="searchFormProvideData.errors.value.name"
        />
      </div>
      <div
        class="tw:pt-3 tw:tl:pt-0 tw:pb-[1rem]"
        v-if="
          searchFormProvideData.form.value.type ===
          ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
        "
      >
        <div class="tw:mb-1 tw:text-xs-design">許可番号</div>
          <q-field
            :error="!!searchFormProvideData.errors.value.license_number"
            no-error-icon
            borderless
            hide-bottom-space
          >
            <template v-slot:control>
              <div class="tw:flex tw:w-full tw:gap-3 tw:text-m-design tw:items-center">
                <BaseSingleSelectInput
                  v-model="form.province"
                  class="tw:w-[40%]"
                  :options="optionsRegions"
                  :use-input="false"
                  :clearable="false"
                  emit-value
                  map-options
                  :error="!!searchFormProvideData.errors.value.license_number"
                >
                  <template v-slot:option="scope">
                    <div>
                      <div
                        class="text-weight-bold text-black q-pl-sm q-pt-xs q-pb-xs"
                        style="pointer-events: none;"
                      >
                        {{ scope.props?.opt?.label }}
                      </div>

                      <div v-for="child in scope.props?.opt?.children" :key="child.value">
                        <q-item
                          dense
                          clickable
                          v-ripple
                          v-close-popup
                          @click="form.province = child.value"
                          :class="{ 'bg-light-blue-1': form.province === child.value }"
                        >
                          <q-item-section>
                            <q-item-label>{{ child.label }}</q-item-label>
                          </q-item-section>
                        </q-item>
                      </div>
                    </div>
                  </template>

                  <template v-slot:selected-item>
                    <div v-if="selectedProvince" class="tw:p-0">
                        <span class="tw:text-m-design">{{ selectedProvince.label }}</span>
                    </div>
                  </template>
                </BaseSingleSelectInput>
                <span class="tw:px-2">
                  うなぎ第
                </span>
                <BaseInput
                  v-model="form.subCode"
                  autocomplete="nope"
                  maxlength="3"
                  inputmode="numeric"
                  outlined
                  input-class="tw:text-m-design tw:text-[#333333]"
                  class="tw:w-[20%]"
                  :error="!!searchFormProvideData.errors.value.license_number"
                  :mask="{
                    mask: /^\d{0,3}$/,
                  }"
                >
                </BaseInput>
                <span>
                  号
                </span>
              </div>
            </template>
            <template v-slot:error>
              <div class="tw:pl-6 tw:transform tw:translate-y-[-1.5rem] tw:tl:translate-y-[-.5rem]">
                {{ searchFormProvideData.errors.value.license_number }}
              </div>
            </template>
          </q-field>
      </div>
      <div class="tw:pt-3 tw:tl:pt-0 tw:pb-[3rem]" v-else>
        <div class="tw:mb-1 tw:text-xs-design">
          ユーザーID（届出番号-数字4桁）
        </div>
        <BaseInput
          v-model="searchFormProvideData.form.value.userCode"
          outlined
          input-class="tw:text-[#333333] tw:text-m-design"
          maxlength="12"
          autocomplete="nope"
          :error="!!searchFormProvideData.errors.value.user_code"
          :error-message="searchFormProvideData.errors.value.user_code"
          inputmode="numeric"
          :mask="{
            mask: '0000000-0000',
          }"
        />
      </div>
    </div>
  </form>
</template>

<script setup>
// #region import
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import BaseSingleSelectInput from 'src/components/base/vs/BaseSingleSelectInput.vue';
import {
  ENTERPRISE_TYPE_ENUM,
  ENTERPRISE_TYPE_OPTIONS,
} from 'src/helpers/constants';
import regionsService from 'src/shared/services/regions.service';
import { ref } from 'vue';
import { computed, onMounted } from 'vue';
import { inject, watch } from 'vue';
// #endregion

// #region state
const optionsRegions = ref([]);
const searchFormProvideData = inject('searchFormProvideData');

// for control license (new spec)
const form = ref({
  province: '',
  subCode: '',
});
// #endregion

// #region functions
const selectedProvince = computed(() => {
  const provinceId = form.value.province;
  for (const region of optionsRegions.value) {
    const found = region.children.find(p => p.value === provinceId);
    if (found) {return found;}
  }
  return null;
});
// #endregion

// #region watch
watch(
  () => searchFormProvideData.form.value.type,
  () => {
    if (
      searchFormProvideData.form.value.type ===
      ENTERPRISE_TYPE_ENUM.EEL_FARMING_ENTERPRISE
    ) {
      searchFormProvideData.form.value.userCode = '';
      searchFormProvideData.errors.value.user_code = '';
    } else {
      searchFormProvideData.form.value.licenseNumber = '';
      searchFormProvideData.errors.value.license_number = '';
      form.value = {
        province: '',
        subCode: '',
      };
    }
  }
);

watch(
  () => form.value.province + form.value.subCode,
  () => {
    if (
      !form.value.province || !form.value.subCode
    ) {
      searchFormProvideData.form.value.licenseNumber = '';
    } else {
      searchFormProvideData.form.value.licenseNumber = `${selectedProvince.value.label}うなぎ第${form.value.subCode}号`;
    }
  }
);
// #endregion

// #region lifecycle hook
onMounted(async () => {
  const listRegions = await regionsService.getRegionWithProvinces();
  optionsRegions.value = listRegions.payload.items.map(item => ({
    label: item.region_name,
    children: item.province.map(province => ({
      label: province.name_short,
      value: province.id,
    })),
  }));
});
// #endregion
</script>
