<template>
  <q-card
    class="tw:p-5 tw:flex tw:flex-col tw:h-full tw:pb-[19rem] tw:tl:pb-[8rem] tw:text-[#333333]"
  >
    <div class="tw:text-l-design tw:font-bold">入荷実績詳細</div>
    <div class="tw:text-m-design tw:mt-5">
      <!-- code -->
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 漁獲/荷口番号 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:text-m-bold-design tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ maskCodeString(detailArrival.code || '') }}</span>
        </div>
      </div>

      <!-- supplier -->
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 仕入先 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>
            {{
              `${detailArrival.starting_user_name || detailArrival.starting_enterprise_name || ''}`
            }}
          </span>
        </div>
      </div>

      <!-- license_number -->
      <div
        v-if="
          detailArrival.starting_user?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE
        "
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 許可番号 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ `${detailArrival.starting_license_number || ''}` }}</span>
        </div>
      </div>

      <!-- note 1 -->
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 備考１ </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ `${detailArrival.starting_user_note_1 || ''}` }}</span>
        </div>
      </div>

      <!-- note 2 -->
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 備考２ </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ `${detailArrival.starting_user_note_2 || ''}` }}</span>
        </div>
      </div>

      <!-- destination user name -->
      <div
        v-if="
          (user.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE &&
            user.staff_type === STAFF_TYPE_ENUM.STAFF &&
            user.name !== detailArrival.destination_user_name) ||
          (user.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
            user.staff_type === STAFF_TYPE_ENUM.ENTERPRISE &&
            user.name !== detailArrival.destination_user_name)
        "
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 入荷者 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <span>{{ `${detailArrival.destination_user_name || ''}` }}</span>
        </div>
      </div>

      <div
        v-if="
          detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL ||
          detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY
        "
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:justify-between"
        >
          <span> 入荷登録単位 </span>
        </div>
        <div
          class="tw:tl:w-[70%] tw:min-h-[4.25rem] tw:bg-white tw:px-5 tw:py-3
          tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <q-radio
            class="tw:text-m-design"
            v-model="arrivalType"
            val="1"
            label="重量"
            size="lg"
          />
          <q-radio
            class="tw:text-m-design tw:pl-3"
            v-model="arrivalType"
            val="2"
            label="尾数"
            size="lg"
          />
        </div>
      </div>

      <!-- arrival weight -->
      <div
        class="tw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:tl:justify-between"
        >
          <span> 入荷量 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2 tw:mt-1"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-5 tw:tl:py-2 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <div
            v-if="
              (arrivalType === '1' &&
                detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL) ||
              (arrivalType === '1' && detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY) ||
              detailArrival.shipping_type === SHIPPING_TYPE_ENUM.NORMAL
            "
            class="tw:grid tw:grid-cols-1 tw:gap-x-10 tw:gap-y-3 tw:tl:grid-cols-2"
          >
            <div
              class="tw:flex tw:gap-3 tw:tl:items-center tw:flex-col tw:tl:flex-row"
            >
              <!-- 全体重量 -->
              <span class="tw:text-xs-design tw:min-w-[6rem]">全体重量</span>
              <BaseInput
                outlined
                type="text"
                maxlength="12"
                inputmode="numeric"
                input-class="tw:text-right tw:text-m-bold-design"
                :model-value="arrivalGrossWeight"
                @maskedUpdated="changeGrossWeight"
                :mask="{
                  mask: Number, // Chỉ chấp nhận số
                  thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                  scale: 2, // Không cho phép số thập phân
                  signed: false, // Không cho phép số âm
                  min: 0, // Chỉ cho phép số không âm
                  lazy: false, // Hiển thị placeholder ngay lập tức
                  radix: '.',
                  max: 9999999.99,
                  padFractionalZeros: false,
                  normalizeZeros: true,
                }"
                :class="{
                  'tw:mb-[1.75rem] tw:tl:mb-0':
                    !!errors.arrivalGrossWeight || !!errors.arrivalTareWeight,
                }"
                :error="!!errors.arrivalGrossWeight"
                :error-message="errors.arrivalGrossWeight"
                suffix="g"
                :is-clearable="!!arrivalGrossWeight"
              >
              </BaseInput>
            </div>
            <div class="tw:flex tw:items-center tw:relative">
              <!-- subtraction sign sm -->
              <div
                class="tw:h-1 tw:bg-[#7E8093] tw:px-4 tw:mt-12 tw:mx-5 tw:tl:hidden"
              />
              <div
                class="tw:flex-1 tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:gap-3"
              >
                <!-- subtraction sign tl -->
                <div
                  class="tw:h-0.75 tw:bg-[#7E8093] tw:px-3 tw:mb-8 tw:mx-5 tw:hidden tw:tl:block tw:absolute tw:top-[2rem] tw:-left-[3.25rem]"
                />
                <!-- 風袋 -->
                <span class="tw:text-xs-design tw:min-w-[3rem]">風袋</span>
                <BaseInput
                  maxlength="12"
                  outlined
                  type="text"
                  inputmode="numeric"
                  :model-value="arrivalTareWeight"
                  @maskedUpdated="changeTareWeight"
                  :mask="{
                    mask: Number, // Chỉ chấp nhận số
                    thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                    scale: 2, // Không cho phép số thập phân
                    signed: false, // Không cho phép số âm
                    min: 0, // Chỉ cho phép số không âm
                    lazy: false, // Hiển thị placeholder ngay lập tức
                    radix: '.',
                    max: 9999999.99,
                    padFractionalZeros: false,
                    normalizeZeros: true,
                  }"
                  input-class="tw:text-right tw:text-m-bold-design"
                  :class="{
                    'tw:mb-[1.75rem] tw:tl:mb-0':
                      !!errors.arrivalGrossWeight || !!errors.arrivalTareWeight,
                  }"
                  :error="!!errors.arrivalTareWeight"
                  :error-message="errors.arrivalTareWeight"
                  suffix="g"
                  :is-clearable="!!arrivalTareWeight"
                >
                </BaseInput>
              </div>
            </div>
            <!-- Divider -->
            <div
              class="tw:tl:col-span-2 tw:h-[1px] tw:bg-[#CBCBCB] tw:mt-2 tw:md:hidden"
            />
            <div
              class="tw:tl:col-span-2 tw:flex tw:items-center tw:justify-between tw:md:justify-start tw:gap-4 tw:mt-5"
            >
              <!-- 入荷量 -->
              <span class="tw:text-xs-design">入荷量</span>
              <div>
                <span class="tw:text-m-bold-design">
                  {{ doParseFloatNumber(arrivalNetWeight) !== 0 ? arrivalNetWeight : '000,000.00' }}g
                </span>
              </div>
            </div>
          </div>
          <div
            v-if="
              (arrivalType === '2' &&
                detailArrival.shipping_type === SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL) ||
              (arrivalType === '2' && detailArrival.shipping_type === SHIPPING_TYPE_ENUM.PROXY)
            "
            class="tw:tl:w-[50%]"
          >
            <BaseInput
              outlined
              type="text"
              maxlength="6"
              inputmode="numeric"
              input-class="tw:text-right tw:text-m-bold-design"
              :model-value="arrivalQuantity"
              @maskedUpdated="changeArrivalQuantity"
              :mask="{
                mask: Number, // Chỉ chấp nhận số
                thousandsSeparator: ',', // Dấu phẩy phân cách hàng nghìn
                signed: false, // Không cho phép số âm
                min: 0, // Chỉ cho phép số không âm
                lazy: false, // Hiển thị placeholder ngay lập tức
                padFractionalZeros: false,
                normalizeZeros: true,
              }"
              :class="{
                'tw:mb-[3.5rem]':
                  !!errors.arrivalQuantity || !!errors.arrivalTareWeight,
              }"
              :error="!!errors.arrivalQuantity"
              :error-message="errors.arrivalQuantity"
              suffix="尾"
              :is-clearable="!!arrivalQuantity"
            >
            </BaseInput>
          </div>
        </div>
      </div>

      <!-- arrival date -->
      <div
        class="ttw:min-h-[4.25rem] tw:flex tw:flex-col tw:tl:flex-row tw:tl:items-center tw:bg-[#E2E3EA] tw:border tw:border-t-0 tw:border-[#D2D2D2]"
      >
        <div
          class="tw:tl:w-[30%] tw:px-5 tw:min-h-[4.25rem] tw:flex tw:items-center tw:tl:justify-between"
        >
          <span> 入荷日 </span>
          <q-badge
            class="tw:text-xxs-design tw:bg-[#E80F00] tw:text-white tw:px-2 tw:ml-2 tw:mt-1"
          >
            必須
          </q-badge>
        </div>
        <div
          class="tw:tl:w-[70%] tw:bg-white tw:px-5 tw:py-3 tw:border-t-1 tw:tl:border-t-0 tw:tl:border-l-1 tw:border-[#D2D2D2]"
        >
          <BaseDatePicker
            v-model="arrivalDate"
            :error="!!errors.arrivalDate"
            :error-message="errors.arrivalDate"
            input-class="tw:text-m-bold-design"
            class="tw:tl:w-[40%]"
            :class="{
              'tw:mb-[2rem]':
                !!errors.arrivalDate,
            }"
          />
        </div>
      </div>
    </div>
    <q-footer
      elevated
      class="tw:bg-white tw:p-3 tw:shadow-[0_-4px_8px_-2px_rgba(0,0,0,0.1)] tw:w-full tw:items-center
      tw:flex tw:justify-center tw:tl:justify-between tw:min-h-[91px] tw:tl:h-[6.5rem] tw:flex-col tw:gap-4 tw:tl:flex-row"
    >
      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-white tw:text-blue-3 tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="修正をやめる"
        @click.prevent="goToPage('arrivalDetail')"
      />

      <BaseButton
        outline
        class="tw:rounded-[40px]"
        :class="`tw:bg-[#004AB9] tw:text-white tw:text-m-design tw:tl:font-bold
        tw:tl:w-[18.9rem] tw:tl:max-h-[4.75rem] tw:tl:min-h-[4.75rem] tw:h-[4.75rem]
        tw:w-full`"
        label="確認する"
        @click.prevent="editArrival"
      />
    </q-footer>
  </q-card>
  <PopupConfirmArrival />
  <ReasonDifference />
</template>
<script setup>
// #region import
import { ref, onMounted, provide, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useAppStore } from 'stores/app-store';
import { useRouter } from 'vue-router';
import arrivalService from 'services/arrival.service';
import {
  maskCodeString,
  FORMAT_NUMBER,
  isNumeric,
  doParseFloatNumber,
  FORMAT_DATE,
} from 'helpers/common';
import { makeQuantityXML, makeXML } from 'boot/print';
import dayjs from 'boot/dayjs';
import {
  UNIT_TYPE_SETTING_ENUM,
  SHIPPING_TYPE_ENUM,
  ADMIN_SYSTEM_SETTING_KEYS_ENUM,
} from 'helpers/constants';
import useValidate from 'composables/validate';
import BaseDatePicker from 'components/base/vs/BaseDatePicker.vue';
import PopupConfirmArrival from 'components/PopupConfirmArrival.vue';
import editArrivalSchema from 'schemas/editArrival';
import MESSAGE from 'helpers/message';
import toast from 'utilities/toast';
import {
  ENTERPRISE_TYPE_ENUM,
  STAFF_TYPE_ENUM,
  TYPE_DIFFERENCE_WEIGHT_ENUM,
} from 'src/helpers/constants';
import { useAuthStore } from 'src/stores/auth-store';
import BaseButton from 'src/components/base/vs/BaseButton.vue';
import BaseInput from 'src/components/base/vs/BaseInput.vue';
import systemSettingsAdminService from 'src/shared/services/admin/systemSettings.admin.service';
import Decimal from 'decimal.js';

import ReasonDifference from './components/ReasonDifference.vue';

// #endregion import

// #region variable
const { settingUser } = storeToRefs(useAppStore());
const router = useRouter();
const { errors, validateData } = useValidate();
const { user } = storeToRefs(useAuthStore());
const isStaff = ref(false);
const isHasLicense = ref(false);
const detailArrival = ref({});
const arrivalId = ref();
const arrivalType = ref('1');
const arrivalDate = ref('');
const arrivalGrossWeight = ref();
const arrivalTareWeight = ref();
const arrivalNetWeight = ref();
const arrivalQuantity = ref();
const unitPerGram = ref(settingUser.value?.unit_per_gram ?? 0);
const shippingNetWeight = ref();
const isPopupReason = ref(false);
const typeDiff = ref(TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH);
const reasonDiff = ref('');
const reasonInfo = ref({});
const systemSetting = ref(null);
const originalArrivalData = ref({});
const hasChangedArrivalData = ref(false);

// #endregion variable

// #region function
const changeGrossWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalGrossWeightNum = doParseFloatNumber(newValue || 0);
  const arrivalTareWeightNum = doParseFloatNumber(arrivalTareWeight.value || 0);
  const arrivalNetWeightNum = arrivalGrossWeightNum - arrivalTareWeightNum;
  arrivalGrossWeight.value = newValue ? FORMAT_NUMBER(arrivalGrossWeightNum) : newValue;
  arrivalNetWeight.value =
    arrivalNetWeightNum >= 0 ? FORMAT_NUMBER(arrivalNetWeightNum, 2) : undefined;
  arrivalQuantity.value =
    arrivalNetWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((arrivalNetWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
  checkArrivalDataChanges();
};

const changeTareWeight = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalGrossWeightNum = doParseFloatNumber(arrivalGrossWeight.value || 0);
  const arrivalTareWeightNum = doParseFloatNumber(newValue || 0);
  const arrivalNetWeightNum = arrivalGrossWeightNum - arrivalTareWeightNum;
  arrivalTareWeight.value = newValue ? FORMAT_NUMBER(arrivalTareWeightNum) : newValue;
  arrivalNetWeight.value =
    arrivalNetWeightNum >= 0 ? FORMAT_NUMBER(arrivalNetWeightNum, 2) : undefined;
  arrivalQuantity.value =
    arrivalNetWeightNum >= 0
      ? FORMAT_NUMBER(Math.ceil((arrivalNetWeightNum / unitPerGram.value).toFixed(3)))
      : undefined;
  checkArrivalDataChanges();
};

const changeArrivalQuantity = newValue => {
  if (!isNumeric(newValue)) {
    return;
  }
  const arrivalQuantityNum = doParseFloatNumber(newValue || 0);
  arrivalTareWeight.value = undefined;
  arrivalQuantity.value = newValue ? FORMAT_NUMBER(arrivalQuantityNum, 0) : newValue;
  const arrivalNetWeightNum =
    Math.ceil(doParseFloatNumber(arrivalQuantity.value) * (unitPerGram.value * 100)) / 100;
  arrivalNetWeight.value = doParseFloatNumber(arrivalQuantity.value)
    ? FORMAT_NUMBER(arrivalNetWeightNum, 2)
    : undefined;
  arrivalGrossWeight.value =
    doParseFloatNumber(arrivalQuantity.value) || doParseFloatNumber(arrivalQuantity.value) === 0
      ? FORMAT_NUMBER(arrivalNetWeightNum)
      : undefined;
  if (newValue === '') {
    arrivalGrossWeight.value = '';
  }
  checkArrivalDataChanges();
};

const confirmFunc = async () => {
  const data = {
    arrivalDate: arrivalDate.value,
    arrivalGrossWeight: doParseFloatNumber(arrivalGrossWeight.value),
    arrivalTareWeight: doParseFloatNumber(arrivalTareWeight.value) || undefined,
    arrivalQuantity: doParseFloatNumber(arrivalQuantity.value) || undefined,
    typeDiff: typeDiff.value || undefined,
    reasonDiff: reasonDiff.value || undefined,
  };
  const result = await arrivalService.editArrival(arrivalId.value, data);
  if (result.code === 402) {
    router.push('home');
    return;
  }
  if (result.code === 0) {
    if (
      hasChangedArrivalData.value &&
      detailArrival.value.shipping_type === SHIPPING_TYPE_ENUM.PROXY
    ) {
      await printReceipt();
    }
    toast.access(MESSAGE.MSG_FIXED_ARRIVAL_INFO);
    await router.push({
      name: 'arrivalList',
    });
  }
};

const popupConfirmArrival = ref({
  isPopup: false,
  titlePopup: '修正確認',
  captionPopup: '以下の内容で、入荷実績を修正します',
  listItems: [],
  confirmFunc,
  minWidthDefault: 60,
  minWidthTlDefault: 80,
  minWidthDtDefault: 123,
});

const editArrival = async () => {
  // Data complate
  setTimeout(() => {
    errors.value = {};
    const data = {
      arrivalDate: arrivalDate.value,
      arrivalGrossWeight:
        arrivalGrossWeight.value === '' ? '' : doParseFloatNumber(arrivalGrossWeight.value) || 0,
      arrivalTareWeight: arrivalTareWeight.value
        ? doParseFloatNumber(arrivalTareWeight.value)
        : undefined,
      arrivalQuantity:
        arrivalType.value === '2' ? doParseFloatNumber(arrivalQuantity.value) ?? '' : undefined,
    };

    const valid = validateData(editArrivalSchema, data);
    if (!valid) {
      return;
    }

    reasonInfo.value = {
      shipping_net_weight: shippingNetWeight.value || 0,
      arrivalNetWeight: arrivalNetWeight.value,
    };
    if (settingUser.value?.display_shipment_weight) {
      reasonInfo.value.displayShipment = settingUser.value?.display_shipment_weight;
    }
    const shippingWeight = +doParseFloatNumber(shippingNetWeight.value) || 0;
    if (
      (Math.abs(shippingWeight - +doParseFloatNumber(arrivalNetWeight.value)) / shippingWeight) *
        100 >
      systemSetting.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
    ) {
      if (!detailArrival.value?.type_diff) {
        typeDiff.value = TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH;
        reasonDiff.value = '';
      }

      isPopupReason.value = true;
      return;
    } else {
      typeDiff.value = null;
      reasonDiff.value = null;
    }

    const amountValue = arrivalType.value === '2' ? convertAmountStockNumber() : convertAmountStock();
    popupConfirmArrival.value.listItems = [
      { key: '入荷登録単位', value: convertArrivalRegistration() },
      { key: '入荷量', badge: true, value: amountValue, vHtml: true },
      { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
    ];

    if (![SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL, SHIPPING_TYPE_ENUM.PROXY].includes(detailArrival.value.shipping_type)) {
      popupConfirmArrival.value.listItems = [
        { key: '入荷量', badge: true, value: amountValue, vHtml: true },
        { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
      ];
    }

    if (arrivalType.value === '2') {
      if (settingUser.value?.display_shipment_weight) {
        reasonInfo.value.displayShipment = settingUser.value?.display_shipment_weight;
      }
      if (
        new Decimal((Math.abs(shippingWeight - (+doParseFloatNumber(arrivalNetWeight.value))) / shippingWeight) *
          100) >
        systemSetting.value[ADMIN_SYSTEM_SETTING_KEYS_ENUM.WEIGHT_ALERT_THRESHOLD]
      ) {
        if (!detailArrival.value?.type_diff) {
          typeDiff.value = TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH;
          reasonDiff.value = '';
        }
        isPopupReason.value = true;
        return;
      } else {
        typeDiff.value = null;
        reasonDiff.value = null;
      }
    }
    popupConfirmArrival.value.isPopup = true;
  }, 200);
};

const goToPage = name => {
  router.push({ name });
};

const convertArrivalRegistration = () => (arrivalType.value === '1' ? '重量' : '尾数');

// Format reason difference display value based on type
const formatReasonDifferenceValue = () => {
  const reasonOptions = [
    { label: '斃死', value: TYPE_DIFFERENCE_WEIGHT_ENUM.DEATH },
    { label: '計量誤差', value: TYPE_DIFFERENCE_WEIGHT_ENUM.WEIGHT_ERROR },
    { label: 'その他', value: TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER },
  ];

  const selectedReason = reasonOptions.find(item => item.value === +typeDiff.value);
  if (!selectedReason) {
    return '';
  }

  if (+typeDiff.value === TYPE_DIFFERENCE_WEIGHT_ENUM.OTHER) {
    return `${selectedReason.label}（${reasonDiff.value}）`;
  }

  return selectedReason.label;
};

function convertAmountStock() {
  arrivalTareWeight.value = arrivalTareWeight.value ? arrivalTareWeight.value : '0';
  if (arrivalType.value === '2') {
    return convertAmountStockNumber();
  }

  const amountStock = `
  <div class="">
  <div class="tw:flex tw:items-center tw:gap-2">
  <div class="tw:text-m-design">${arrivalGrossWeight.value || '0'} g</div>
  <svg width="25" height="48" viewBox="0 0 41 65" fill="none" class="tw:text-[#7E8093]"
    xmlns="http://www.w3.org/2000/svg">
    <rect x="8.5" y="33" width="24" height="2" fill="#7E8093"/>
  </svg>
  <div class="tw:text-m-design tw:text-[#333333]">${arrivalTareWeight.value || '0'} g</div>
  </div>
  <div class="tw:text-m-design tw:text-[#333333]"><span class="tw:text-xs-design">入荷量</span> ${arrivalNetWeight.value || '0'} g</div>
  </div>
  `;
  return amountStock;
}

function convertAmountStockNumber() {
  const amountStockNum = `
    <div class="tw:text-m-design tw:pt-2">${arrivalQuantity.value} 尾</div>
  `;
  return amountStockNum;
}

const confirmReason = () => {
  const amountValue = arrivalType.value === '2' ? convertAmountStockNumber() : convertAmountStock();

  popupConfirmArrival.value.listItems = [
    { key: '入荷登録単位', value: convertArrivalRegistration() },
    { key: '入荷量', badge: true, value: amountValue, vHtml: true },
    { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
  ];

  if (![SHIPPING_TYPE_ENUM.ARRIVAL_MANUAL, SHIPPING_TYPE_ENUM.PROXY].includes(detailArrival.value.shipping_type)) {
    popupConfirmArrival.value.listItems = [
      { key: '入荷量', badge: true, value: amountValue, vHtml: true },
      { key: '入荷日', value: `${arrivalDate.value}`, badge: true },
    ];
  }

  if (arrivalType.value === '2') {
    popupConfirmArrival.value.listItems[1] = {
      key: '入荷量',
      badge: true,
      value: convertAmountStockNumber(),
      vHtml: true,
    };
  }
  if (reasonDiff.value) {
    popupConfirmArrival.value.listItems.push({
      key: '差異の理由',
      value: formatReasonDifferenceValue(),
      badge: true,
    });
  }
  popupConfirmArrival.value.isPopup = true;
};

const printReceipt = async () => {
  const receiptNumber = settingUser.value?.receipt_number || 1;
  if (arrivalType.value === '2') {
    const dataPrint = makeQuantityXML(
      detailArrival.value.destination_enterprise_name,
      detailArrival.value.destination_user_name,
      maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
      FORMAT_NUMBER(arrivalQuantity.value),
      dayjs(FORMAT_DATE(arrivalDate.value)).toDate(),
      settingUser.value?.price_per_quantity?.[0] || '',
      FORMAT_NUMBER(
        doParseFloatNumber(arrivalQuantity.value) *
          (detailArrival.value?.setting?.price_per_quantity?.[0] || 0) || ''
      ),
      detailArrival.value.starting_enterprise_name || detailArrival.value.starting_user_name,
      detailArrival.value.starting_user_name,
      detailArrival.value.starting_license_number || '',
      detailArrival.value.starting_user_note_1 || '',
      receiptNumber
    );
    const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;
    const aTag = document.createElement('a');
    aTag.href = href;
    aTag.click();
  } else {
    const pricePerKilogram = detailArrival.value?.setting?.price_per_kilogram || 0;
    const price = Number(doParseFloatNumber(arrivalNetWeight.value)) * Number(pricePerKilogram);

    const dataPrint = makeXML(
      detailArrival.value.destination_enterprise_name,
      detailArrival.value.destination_user_name,
      maskCodeString(detailArrival.value.code?.replaceAll('-', '')),
      arrivalGrossWeight.value || '',
      arrivalTareWeight.value || '',
      arrivalNetWeight.value || '',
      dayjs(FORMAT_DATE(arrivalDate.value)).toDate(),
      pricePerKilogram || '',
      FORMAT_NUMBER(price || ''),
      detailArrival.value.starting_enterprise_name || detailArrival.value.starting_user_name,
      detailArrival.value.starting_user_name,
      detailArrival.value.starting_license_number || '',
      detailArrival.value.starting_user_note_1 || '',
      receiptNumber
    );
    const href = `tmprintassistant://tmprintassistant.epson.com/print?ver=1&data-type=eposprintxml&data=${dataPrint}`;
    const aTag = document.createElement('a');
    aTag.href = href;
    aTag.click();
  }
};

const checkArrivalDataChanges = () => {
  const currentNetWeight =
    arrivalType.value === '1'
      ? doParseFloatNumber(arrivalNetWeight.value)
      : doParseFloatNumber(arrivalQuantity.value);
  const originalNetWeight =
    arrivalType.value === '1'
      ? doParseFloatNumber(originalArrivalData.value.arrival_net_weight)
      : doParseFloatNumber(originalArrivalData.value.arrival_quantity);

  const currentDate = arrivalDate.value;
  const originalDate = FORMAT_DATE(originalArrivalData.value.arrival_date);

  hasChangedArrivalData.value =
    currentNetWeight !== originalNetWeight || currentDate !== originalDate;
};

// #endregion function

// provide
provide('popupConfirmArrival', popupConfirmArrival);
provide('isPopupReason', isPopupReason);
provide('typeDiff', typeDiff);
provide('reasonDiff', reasonDiff);
provide('confirmReason', confirmReason);
provide('reasonInfo', reasonInfo);

watch(arrivalDate, () => {
  checkArrivalDataChanges();
});

onMounted(async () => {
  arrivalId.value = router.currentRoute.value.params?.id;
  arrivalType.value =
    settingUser.value?.unit_type === UNIT_TYPE_SETTING_ENUM.QUANTITY_ONLY ? '2' : '1';
  const result = await arrivalService.getArrivalDetail(arrivalId.value);
  if (result.code === 0) {
    detailArrival.value = result.payload;
    isHasLicense.value =
      result.payload?.destination_user?.enterprise_type === ENTERPRISE_TYPE_ENUM.CATCH_ENTERPRISE;

    isStaff.value =
      result.payload.destination_user?.enterprise_type ===
        ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      result.payload.destination_user?.staff_type === STAFF_TYPE_ENUM.STAFF &&
      user.value?.enterprise_type === ENTERPRISE_TYPE_ENUM.DISTRIBUTE_ENTERPRISE &&
      user.value?.staff_type === STAFF_TYPE_ENUM.ENTERPRISE;

    arrivalDate.value = FORMAT_DATE(result.payload?.arrival_date);
    arrivalGrossWeight.value = FORMAT_NUMBER(result.payload?.arrival_gross_weight);
    arrivalTareWeight.value = FORMAT_NUMBER(result.payload?.arrival_tare_weight);
    arrivalNetWeight.value = FORMAT_NUMBER(result.payload?.arrival_net_weight);
    arrivalQuantity.value = FORMAT_NUMBER(result.payload?.arrival_quantity);
    shippingNetWeight.value = FORMAT_NUMBER(result.payload?.shipping_net_weight);

    if (result.payload?.type_diff) {
      typeDiff.value = result.payload.type_diff;
    }
    if (result.payload?.reason_diff) {
      reasonDiff.value = result.payload.reason_diff;
    }

    originalArrivalData.value = {
      arrival_date: result.payload?.arrival_date,
      arrival_net_weight: result.payload?.arrival_net_weight,
      arrival_quantity: result.payload?.arrival_quantity,
    };

    const systemSettingResponse = await systemSettingsAdminService.getSystemSettingsForNormalUser();
    if (systemSettingResponse) {
      systemSetting.value = systemSettingResponse.payload;
    }
  }
});
</script>

<style scoped>
:deep(.q-field__control-container .q-field__suffix) {
  margin-right: 1rem !important;
  font-size: 2rem;
  line-height: 1.5;
}
</style>
