const BaseService = require('../base/serviceFn');

class RegionService extends BaseService {
  /**
   * Get region list with provinces
   * @returns {Promise<Object>} - regions with provinces
   */
  async getRegionWithProvinces() {
    const connect = this.DB.READ;
    const regions = await connect.regions.findMany({
      where: {
        delete_flag: false,
      },
      orderBy: {
        id: 'asc',
      },
      select: {
        id: true,
        region_name: true,
        province: {
          where: {
            delete_flag: false,
          },
          orderBy: {
            id: 'asc',
          },
          select: {
            id: true,
            name: true,
            name_short: true,
          },
        },
      },
    });

    return this.SUCCESS({
      items: regions,
    });
  }
}

module.exports = RegionService;
