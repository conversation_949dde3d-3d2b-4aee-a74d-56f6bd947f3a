-- -- insert default data tbl users
-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, "status", province_id)
-- VALUES('admin', NULL, NULL, 'admin', NULL, '', '54cf442114ed03851cc48925c6181a5b.ebabc7bbd347bd809bea91011c76c76948ef39ff255b82c08b92603fdd0769a6dd2566c138dde83e7ea2a937bc1bdbcca3de25adb3fd817c9232a1040d9d6501', '00', 0, NULL, '2024-09-24 03:13:39.991', NULL, false, NULL, NULL, NULL, NULL, NULL, 'admin', NULL, 0, 0);

-- -- insert default data tbl enterprises
-- INSERT INTO public.enterprises
-- (enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('9999999', '海外', NULL, '海外', NULL, 99, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);
-- INSERT INTO public.enterprises
-- (enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('9990000', '養殖物（人工種苗）', NULL, '養殖物（人工種苗）', NULL, 98, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);

-- -- Insert users and link them to enterprises
-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id)
-- VALUES('9999999-0000', NULL, (SELECT id FROM public.enterprises WHERE enterprise_code = '9999999'), '', NULL, '', 'password_hash_1', '00', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0);

-- INSERT INTO public.users
-- (user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id)
-- VALUES('9990000-0000', NULL, (SELECT id FROM public.enterprises WHERE enterprise_code = '9990000'), '', NULL, '', 'password_hash_2', '00', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0);

-- -- insert default data tbl sys_settings
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('edit_deadline_for_arrival', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('catching_reserve_period', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- INSERT INTO public.sys_settings
-- (setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
-- VALUES('weight_alert_threshold', '20', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
-- insert default data tbl sys_settings
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('edit_deadline_for_arrival_shipping', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('catching_reserve_period', '14', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);
INSERT INTO public.sys_settings
(setting_name, setting_value, created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES('weight_alert_threshold', '20', 0, NULL, '2024-10-09 06:23:37.916', '2024-10-09 09:30:24.122', false);

INSERT INTO provinces (name, name_short, setting, region_id) VALUES
('北海道', '北海道', '{"is_show_info": 0}', 1),
('青森県', '青森', '{"is_show_info": 0}', 2),
('岩手県', '岩手', '{"is_show_info": 0}', 2),
('宮城県', '宮城', '{"is_show_info": 0}', 2),
('秋田県', '秋田', '{"is_show_info": 0}', 2),
('山形県', '山形', '{"is_show_info": 0}', 2),
('福島県', '福島', '{"is_show_info": 0}', 2),
('茨城県', '茨城', '{"is_show_info": 0}', 3),
('栃木県', '栃木', '{"is_show_info": 0}', 3),
('群馬県', '群馬', '{"is_show_info": 0}', 3),
('埼玉県', '埼玉', '{"is_show_info": 0}', 3),
('千葉県', '千葉', '{"is_show_info": 0}', 3),
('東京都', '東京', '{"is_show_info": 0}', 3),
('神奈川県', '神奈川', '{"is_show_info": 0}', 3),
('新潟県', '新潟', '{"is_show_info": 0}', 4),
('富山県', '富山', '{"is_show_info": 0}', 4),
('石川県', '石川', '{"is_show_info": 0}', 4),
('福井県', '福井', '{"is_show_info": 0}', 4),
('山梨県', '山梨', '{"is_show_info": 0}', 4),
('長野県', '長野', '{"is_show_info": 0}', 4),
('岐阜県', '岐阜', '{"is_show_info": 0}', 4),
('静岡県', '静岡', '{"is_show_info": 0}', 4),
('愛知県', '愛知', '{"is_show_info": 0}', 4),
('三重県', '三重', '{"is_show_info": 0}', 5),
('滋賀県', '滋賀', '{"is_show_info": 0}', 5),
('京都府', '京都', '{"is_show_info": 0}', 5),
('大阪府', '大阪', '{"is_show_info": 0}', 5),
('兵庫県', '兵庫', '{"is_show_info": 0}', 5),
('奈良県', '奈良', '{"is_show_info": 0}', 5),
('和歌山県', '和歌山', '{"is_show_info": 0}', 5),
('鳥取県', '鳥取', '{"is_show_info": 0}', 6),
('島根県', '島根', '{"is_show_info": 0}', 6),
('岡山県', '岡山', '{"is_show_info": 0}', 6),
('広島県', '広島', '{"is_show_info": 0}', 6),
('山口県', '山口', '{"is_show_info": 0}', 6),
('徳島県', '徳島', '{"is_show_info": 0}', 7),
('香川県', '香川', '{"is_show_info": 0}', 7),
('愛媛県', '愛媛', '{"is_show_info": 0}', 7),
('高知県', '高知', '{"is_show_info": 0}', 7),
('福岡県', '福岡', '{"is_show_info": 0}', 8),
('佐賀県', '佐賀', '{"is_show_info": 0}', 8),
('長崎県', '長崎', '{"is_show_info": 0}', 8),
('熊本県', '熊本', '{"is_show_info": 0}', 8),
('大分県', '大分', '{"is_show_info": 0}', 8),
('宮崎県', '宮崎', '{"is_show_info": 0}', 8),
('鹿児島県', '鹿児島', '{"is_show_info": 1}', 8),
('沖縄県', '沖縄', '{"is_show_info": 0}', 8);

INSERT INTO regions (id, region_name, created_by_id, latest_updated_by_id, created_on, delete_flag)
VALUES
(1, '北海道', 0, 0, NOW(), FALSE),
(2, '東北',   0, 0, NOW(), FALSE),
(3, '関東',   0, 0, NOW(), FALSE),
(4, '中部',   0, 0, NOW(), FALSE),
(5, '近畿',   0, 0, NOW(), FALSE),
(6, '中国',   0, 0, NOW(), FALSE),
(7, '四国',   0, 0, NOW(), FALSE),
(8, '九州',   0, 0, NOW(), FALSE);

INSERT INTO public.users
(id, user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id, province_custom_data, locktime, loginfail, account_registration_status, enterprise_type, staff_type, enable_export_function)
VALUES(1, '9999999-0000', NULL, 1, '海外', NULL, '', '', '01', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0, NULL, NULL, 0, 0, 99, 0, false);
INSERT INTO public.users
(id, user_code, qr_code, enterprise_id, "name", name_kana, phone, "password", "role", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag, statistics_date_from, license_id, note_1, note_2, license_number, name_nospace, name_kana_nospace, status, province_id, province_custom_data, locktime, loginfail, account_registration_status, enterprise_type, staff_type, enable_export_function)
VALUES(2, '9990000-0000', NULL, 2, '養殖物（人工種苗）', NULL, '', '', '01', 0, NULL, '2024-09-24 04:00:00.000', NULL, false, NULL, NULL, NULL, NULL, NULL, '', NULL, 0, 0, NULL, NULL, 0, 0, 98, 0, false);

INSERT INTO public.enterprises
(id, enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES(1, '9999999', '海外', NULL, '海外', NULL, 99, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);
INSERT INTO public.enterprises
(id, enterprise_code, enterprise_name, enterprise_name_kana, enterprise_name_nospace, enterprise_name_kana_nospace, "type", created_by_id, latest_updated_by_id, created_on, latest_updated_on, delete_flag)
VALUES(2, '9990000', '養殖物（人工種苗）', NULL, '養殖物（人工種苗）', NULL, 98, 0, NULL, '2024-09-24 03:57:50.411', NULL, false);